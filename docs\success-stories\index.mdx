---
title: "Success Stories"
description: "Real-world examples of what you can achieve with OpenHands"
---

Discover how developers and teams are using OpenHands to automate their software development workflows. From quick fixes to complex projects, see what's possible with AI-powered development assistance.

Check out the [#success-stories](https://www.linen.dev/s/openhands/c/success-stories) channel on our Slack for more!

<Update label="2025-06-13 OpenHands helps frontline support" description="@Joe <PERSON>tier">

## One of the cool things about OpenHands, and especially the Slack Integration, is the ability to empower folks who are on the ‘front lines’ with customers.

For example, often times Support and Customer Success teams will field bug reports, doc questions, and other ‘nits’ from customers. They tend to have few options to deal with this, other than file a feedback ticket with product teams and hope it gets prioritized in an upcoming sprint.

Instead, with tools like OpenHands and the Slack integration, they can request OpenHands to make fixes proactively and then have someone on the engineering team (like a lead engineer, a merge engineer, or even technical product manager) review the PR and approve it — thus reducing the cycle time for ‘quick wins’ from weeks to just a few hours.

Here's how we do that with the OpenHands project:

<iframe
  width="560"
  height="560"
  src="https://www.linen.dev/s/openhands/t/29118545/seems-mcp-config-from-config-toml-is-being-overwritten-hence#629f8e2b-cde8-427e-920c-390557a06cc9"
  frameborder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowfullscreen
></iframe>

[Original Slack thread](https://www.linen.dev/s/openhands/t/29124350/one-of-the-cool-things-about-openhands-and-especially-the-sl#25029f37-7b0d-4535-9187-83b3e06a4011)

</Update>


<Update label="2025-06-13 Ask OpenHands to show me some love" description="@Graham Neubig">

## Asked openhands to “show me some love” and...

Asked openhands to “show me some love” and it coded up this app for me, actually kinda genuinely feel loved

<video
  controls
  autoplay
  className="w-full aspect-video"
  src="/success-stories/stories/2025-06-13-show-love/v1.mp4"
></video>

[Original Slack thread](https://www.linen.dev/s/openhands/t/29100731/asked-openhands-to-show-me-some-love-and-it-coded-up-this-ap#1e08af6b-b7d5-4167-8a53-17e6806555e0)

</Update>

<Update label="2025-06-11 OpenHands does 100% of my infra IAM research for me" description="@Xingyao Wang">

## Now, OpenHands does 100% of my infra IAM research for me

Got an IAM error on GCP? Send a screenshot to OH... and it just works!!!
Can't imagine going back to the early days without OH: I'd spend an entire afternoon figuring how to get IAM right

[Original Slack thread](https://www.linen.dev/s/openhands/t/29100732/now-openhands-does-100-of-my-infra-iam-research-for-me-sweat#20482a73-4e2e-4edd-b6d1-c9e8442fccd1)

![](/success-stories/stories/2025-06-11-infra-iam/s1.png)
![](/success-stories/stories/2025-06-11-infra-iam/s2.png)

</Update>

<Update label="2025-06-08 OpenHands builds an interactive map for me" description="@Rodrigo Argenton Freire (ODLab)">

## Very simple example, but baby steps....

I am a professor of architecture and urban design. We built, me and some students, an interactive map prototype to help visitors and new students to find important places in the campus. Considering that we lack a lot of knowledge in programming, that was really nice to build and a smooth process.
We first created the main components with all-hands and then adjusted some details locally. Definitely, saved us a lot of time and money.
That's a prototype but we will have all the info by tuesday.
https://buriti-emau.github.io/Mapa-UFU/

[Original Slack thread](https://www.linen.dev/s/openhands/t/29100736/very-simple-example-but-baby-steps-i-am-a-professor-of-archi#8f2e3f3f-44e6-44ea-b9a8-d53487470179)

![](/success-stories/stories/2025-06-08-map/s1.png)

</Update>


<Update label="2025-06-06 Web Search Saves the Day" description="@Ian Walker">

## Tavily adapter helps solve persistent debugging issue

Big congratulations to the new [Tavily adapter](https://www.all-hands.dev/blog/building-a-provably-versatile-agent)... OpenHands and I have been beavering away at a Lightstreamer client library for most of this week but were getting a persistent (and unhelpful) "unexpected error" from the server.

Coming back to the problem today, after trying several unsuccessful fixes prompted by me, OH decided all by itself to search the web, and found the cause of the problem (of course it was simply CRLF line endings...). I was on the verge of giving up - good thing OH has more stamina than me!

This demonstrates how OpenHands' web search capabilities can help solve debugging issues that would otherwise require extensive manual research.

<iframe
  width="560"
  height="560"
  src="https://www.linen.dev/s/openhands/t/29100737/big-congratulations-to-the-new-tavily-adapter-openhands-and-#87b027e5-188b-425e-8aa9-719dcb4929f4"
  title="YouTube video player"
  frameborder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowfullscreen
></iframe>


[Original Slack thread](https://www.linen.dev/s/openhands/t/29100737/big-congratulations-to-the-new-tavily-adapter-openhands-and-#76f1fb26-6ef7-4709-b9ea-fb99105e47e4)

</Update>

<Update label="2025-06-05 OpenHands updates my personal website for a new paper" description="@Xingyao Wang">

## I asked OpenHands to update my personal website for the "OpenHands Versa" paper.

It is an extremely trivial task: You just need to browse to arxiv, copy the author names, format them for BibTeX, and then modify the papers.bib file. But now I'm getting way too lazy to even open my IDE and actually do this one-file change!

[Original Tweet/X thread](https://x.com/xingyaow_/status/1930796287919542410)

[Original Slack thread](https://www.linen.dev/s/openhands/t/29100738/i-asked-openhands-to-update-my-personal-website-for-the-open#f0324022-b12b-4d34-b12b-bdbc43823f69)

</Update>

<Update label="2025-06-02 OpenHands makes an animated gif of swe-bench verified scores over time" description="@Graham Neubig">

## I asked OpenHands to make an animated gif of swe-bench verified scores over time.

It took a bit of prompting but ended up looking pretty nice I think

<video width="560" height="315" autoPlay loop muted src="/success-stories/stories/2025-06-02-swebench-score/s1.mp4"></video>

[Original Slack thread](https://www.linen.dev/s/openhands/t/29100744/i-asked-openhands-to-make-an-animated-gif-of-swe-bench-verif#fb3b82c9-6222-4311-b97b-b2ac1cfe6dff)

</Update>

<Update label="2025-05-30 AWS Troubleshooting" description="@Graham Neubig">

## Quick AWS security group fix

I really don't like trying to fix issues with AWS, especially security groups and other finicky things like this. But I started up an instance and wasn't able to ssh in. So I asked OpenHands:

> Currently, the following ssh command is timing out:
>
> $ ssh -i gneubig.pem <EMAIL>
> ssh: connect to host XXX.us-east-2.compute.amazonaws.com port 22: Operation timed out
>
> Use the provided AWS credentials to take a look at i-XXX and examine why

And 2 minutes later I was able to SSH in!

This shows how OpenHands can quickly diagnose and fix AWS infrastructure issues that would normally require manual investigation.

[Original Slack thread](https://www.linen.dev/s/openhands/t/29100747/i-really-don-t-like-trying-to-fix-issues-with-aws-especially#d92a66d2-3bc1-4467-9d09-dc983004d083)

</Update>


<Update label="2025-05-04 Chrome Extension Development" description="@Xingyao Wang">

## OpenHands builds Chrome extension for GitHub integration

I asked OpenHands to write a Chrome extension based on our [OpenHands Cloud API](https://docs.all-hands.dev/modules/usage/cloud/cloud-api). Once installed, you can now easily launch an OpenHands cloud session from your GitHub webpage/PR!

This demonstrates OpenHands' ability to create browser extensions and integrate with external APIs, enabling seamless workflows between GitHub and OpenHands Cloud.

![Chrome extension](/success-stories/stories/2025-05-04-chrome-extension/s1.png)
![Chrome extension](/success-stories/stories/2025-05-04-chrome-extension/s2.png)

[GitHub Repository](https://github.com/xingyaoww/openhands-chrome-extension)

[Original Slack thread](https://www.linen.dev/s/openhands/t/29100755/i-asked-openhands-to-write-a-chrome-extension-based-on-our-h#88f14b7f-f8ff-40a6-83c2-bd64e95924c5)

</Update>


<Update label="2025-04-11 Visual UI Testing" description="@Xingyao Wang">

## OpenHands tests UI automatically with visual browsing

Thanks to visual browsing -- OpenHands can actually test some simple UI by serving the website, clicking the button in the browser and looking at screenshots now!

Prompt is just:
```
I want to create a Hello World app in Javascript that:
* Displays Hello World in the middle.
* Has a button that when clicked, changes the greeting with a bouncing animation to fun versions of Hello.
* Has a counter for how many times the button has been clicked.
* Has another button that changes the app's background color.
```

Eager-to-work Sonnet 3.7 will test stuff for you without you asking!

This showcases OpenHands' visual browsing capabilities, enabling it to create, serve, and automatically test web applications through actual browser interactions and screenshot analysis.

![Visual UI testing](/success-stories/stories/2025-04-11-visual-ui/s1.png)

[Original Slack thread](https://www.linen.dev/s/openhands/t/29100764/thanks-to-u07k0p3bdb9-s-visual-browsing-openhands-can-actual#21beb9bc-1a04-4272-87e9-4d3e3b9925e7)

</Update>

<Update label="2025-03-07 Proactive Error Handling" description="@Graham Neubig">

## OpenHands fixes crashes before you notice them

Interesting story, I asked OpenHands to start an app on port 12000, it showed up on the app pane. I started using the app, and then it crashed... But because it crashed in OpenHands, OpenHands immediately saw the error message and started fixing the problem without me having to do anything. It was already fixing the problem before I even realized what was going wrong.

This demonstrates OpenHands' proactive monitoring capabilities - it doesn't just execute commands, but actively watches for errors and begins remediation automatically, often faster than human reaction time.

</Update>

<Update label="2024-12-03 Creative Design Acceleration" description="@Rohit Malhotra">

## Pair programming for interactive design projects

Used OpenHands as a pair programmer to do heavy lifting for a creative/interactive design project in p5js.

I usually take around 2 days for high fidelity interactions (planning strategy + writing code + circling back with designer), did this in around 5hrs instead with the designer watching curiously the entire time.

This showcases how OpenHands can accelerate creative and interactive design workflows, reducing development time by 75% while maintaining high quality output.

[Original Tweet](https://x.com/rohit_malh5/status/1863995531657425225)

</Update>
