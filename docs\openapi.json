{"openapi": "3.0.3", "info": {"title": "OpenHands API", "description": "OpenHands: Code Less, Make More", "version": "1.0.0"}, "servers": [{"url": "https://app.all-hands.dev", "description": "Production server"}, {"url": "http://localhost:3000", "description": "Local development server"}], "paths": {"/health": {"get": {"summary": "Health check", "description": "Check if the API is running", "operationId": "health", "responses": {"200": {"description": "API is running", "content": {"text/plain": {"schema": {"type": "string", "example": "OK"}}}}}}}, "/api/conversations/{conversation_id}/config": {"get": {"summary": "Get runtime configuration", "description": "Retrieve the runtime configuration (session ID and runtime ID)", "operationId": "getRemoteRuntimeConfig", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "responses": {"200": {"description": "Runtime configuration", "content": {"application/json": {"schema": {"type": "object", "properties": {"runtime_id": {"type": "string", "nullable": true}, "session_id": {"type": "string", "nullable": true}}}}}}}}}, "/api/conversations/{conversation_id}/vscode-url": {"get": {"summary": "Get VSCode URL", "description": "Get the VSCode URL for the conversation", "operationId": "getVscodeUrl", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "responses": {"200": {"description": "VSCode URL", "content": {"application/json": {"schema": {"type": "object", "properties": {"vscode_url": {"type": "string", "nullable": true}}}}}}, "500": {"description": "Error getting VSCode URL", "content": {"application/json": {"schema": {"type": "object", "properties": {"vscode_url": {"type": "string", "nullable": true}, "error": {"type": "string"}}}}}}}}}, "/api/conversations/{conversation_id}/web-hosts": {"get": {"summary": "Get runtime hosts", "description": "Get the hosts used by the runtime", "operationId": "getHosts", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "responses": {"200": {"description": "Runtime hosts", "content": {"application/json": {"schema": {"type": "object", "properties": {"hosts": {"type": "array", "items": {"type": "string"}}}}}}}, "500": {"description": "Error getting runtime hosts", "content": {"application/json": {"schema": {"type": "object", "properties": {"hosts": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"type": "string"}}}}}}}}}, "/api/conversations/{conversation_id}/submit-feedback": {"post": {"summary": "Submit feedback", "description": "Submit user feedback for a conversation", "operationId": "submitFeedback", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "version": {"type": "string"}, "permissions": {"type": "string", "default": "private"}, "polarity": {"type": "string"}, "feedback": {"type": "string"}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> submitted successfully", "content": {"application/json": {"schema": {"type": "object"}}}}, "500": {"description": "Error submitting feedback", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/conversations/{conversation_id}/list-files": {"get": {"summary": "List files", "description": "List files in the specified path", "operationId": "listFiles", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}, {"name": "path", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Path to list files from"}], "responses": {"200": {"description": "List of files", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "404": {"description": "Runtime not initialized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Error listing files", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/conversations/{conversation_id}/select-file": {"get": {"summary": "Get file content", "description": "Retrieve the content of a specified file", "operationId": "selectFile", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}, {"name": "file", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Path of the file to be retrieved"}], "responses": {"200": {"description": "File content", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string"}}}}}}, "415": {"description": "Unable to open binary file", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Error opening file", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/conversations/{conversation_id}/zip-directory": {"get": {"summary": "Download workspace as zip", "description": "Download the current workspace as a zip file", "operationId": "zipCurrentWorkspace", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "responses": {"200": {"description": "Workspace zip file", "content": {"application/zip": {"schema": {"type": "string", "format": "binary"}}}}, "500": {"description": "Error zipping workspace", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/conversations/{conversation_id}/git/changes": {"get": {"summary": "Get git changes", "description": "Get git changes in the workspace", "operationId": "git<PERSON><PERSON>es", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "responses": {"200": {"description": "Git changes", "content": {"application/json": {"schema": {"type": "object"}}}}, "500": {"description": "Error getting git changes", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/conversations/{conversation_id}/git/diff": {"get": {"summary": "Get git diff", "description": "Get git diff for a specific file", "operationId": "gitDiff", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}, {"name": "path", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Path of the file to get diff for"}], "responses": {"200": {"description": "Git diff", "content": {"application/json": {"schema": {"type": "string"}}}}, "500": {"description": "Error getting git diff", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/conversations/{conversation_id}/trajectory": {"get": {"summary": "Get trajectory", "description": "Get the conversation trajectory", "operationId": "getTrajectory", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "responses": {"200": {"description": "Conversation trajectory", "content": {"application/json": {"schema": {"type": "object", "properties": {"trajectory": {"type": "array", "items": {"type": "object"}}}}}}}, "500": {"description": "Error getting trajectory", "content": {"application/json": {"schema": {"type": "object", "properties": {"trajectory": {"type": "array", "items": {"type": "object"}, "nullable": true}, "error": {"type": "string"}}}}}}}}}, "/api/conversations/{conversation_id}/security/{path}": {"get": {"summary": "Security analyzer API (GET)", "description": "Catch-all route for security analyzer API GET requests", "operationId": "securityApiGet", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}, {"name": "path", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Security analyzer API path"}], "responses": {"200": {"description": "Security analyzer response", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"description": "Security analyzer not initialized", "content": {"application/json": {"schema": {"type": "object", "properties": {"detail": {"type": "string"}}}}}}}}, "post": {"summary": "Security analyzer API (POST)", "description": "Catch-all route for security analyzer API POST requests", "operationId": "securityApiPost", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}, {"name": "path", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Security analyzer API path"}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Security analyzer response", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"description": "Security analyzer not initialized", "content": {"application/json": {"schema": {"type": "object", "properties": {"detail": {"type": "string"}}}}}}}}, "put": {"summary": "Security analyzer API (PUT)", "description": "Catch-all route for security analyzer API PUT requests", "operationId": "securityApiPut", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}, {"name": "path", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Security analyzer API path"}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Security analyzer response", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"description": "Security analyzer not initialized", "content": {"application/json": {"schema": {"type": "object", "properties": {"detail": {"type": "string"}}}}}}}}, "delete": {"summary": "Security analyzer API (DELETE)", "description": "Catch-all route for security analyzer API DELETE requests", "operationId": "securityApiDelete", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}, {"name": "path", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Security analyzer API path"}], "responses": {"200": {"description": "Security analyzer response", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"description": "Security analyzer not initialized", "content": {"application/json": {"schema": {"type": "object", "properties": {"detail": {"type": "string"}}}}}}}}}, "/api/conversations": {"post": {"summary": "Create new conversation", "description": "Initialize a new conversation", "operationId": "newConversation", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"repository": {"type": "string", "nullable": true, "description": "Full name of the repository (e.g., owner/repo)"}, "git_provider": {"type": "string", "nullable": true, "description": "The Git provider (e.g., github or gitlab). If omitted, all configured providers are checked for the repository."}, "selected_branch": {"type": "string", "nullable": true}, "initial_user_msg": {"type": "string", "nullable": true}, "conversation_instructions": {"type": "string", "nullable": true, "description": "Optional instructions the agent must follow throughout the conversation while addressing the user's initial task"}, "image_urls": {"type": "array", "items": {"type": "string"}, "nullable": true}, "replay_json": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "Conversation created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "conversation_id": {"type": "string"}}}}}}, "400": {"description": "Error creating conversation", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "message": {"type": "string"}, "msg_id": {"type": "string"}}}}}}}}, "get": {"summary": "Search conversations", "description": "Search for conversations", "operationId": "searchConversations", "parameters": [{"name": "page_id", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Page ID for pagination"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 20}, "description": "Number of conversations to return"}], "responses": {"200": {"description": "Conversations", "content": {"application/json": {"schema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"conversation_id": {"type": "string"}, "title": {"type": "string"}, "last_updated_at": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "selected_repository": {"type": "string", "nullable": true}, "status": {"type": "string", "enum": ["RUNNING", "STOPPED"]}, "trigger": {"type": "string", "enum": ["GUI", "API"]}}}}, "next_page_id": {"type": "string", "nullable": true}}}}}}}}}, "/api/conversations/{conversation_id}": {"get": {"summary": "Get conversation", "description": "Get conversation details", "operationId": "getConversation", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "responses": {"200": {"description": "Conversation details", "content": {"application/json": {"schema": {"type": "object", "properties": {"conversation_id": {"type": "string"}, "title": {"type": "string"}, "last_updated_at": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "selected_repository": {"type": "string", "nullable": true}, "status": {"type": "string", "enum": ["RUNNING", "STOPPED"]}, "trigger": {"type": "string", "enum": ["GUI", "API"]}}}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"type": "object", "nullable": true}}}}}}, "patch": {"summary": "Update conversation", "description": "Update conversation details", "operationId": "updateConversation", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}}}}}}, "responses": {"200": {"description": "Conversation updated successfully", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}, "delete": {"summary": "Delete conversation", "description": "Delete a conversation", "operationId": "deleteConversation", "parameters": [{"name": "conversation_id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Conversation ID"}], "responses": {"200": {"description": "Conversation deleted successfully", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/api/user/repositories": {"get": {"summary": "Get user repositories", "description": "Get repositories for the authenticated user", "operationId": "getUserRepositories", "parameters": [{"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "default": "pushed"}, "description": "Sort order for repositories"}], "responses": {"200": {"description": "User repositories", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"full_name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "html_url": {"type": "string"}, "private": {"type": "boolean"}, "fork": {"type": "boolean"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}}, "401": {"description": "Authentication error", "content": {"application/json": {"schema": {"type": "string"}}}}, "500": {"description": "Unknown error", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/user/info": {"get": {"summary": "Get user info", "description": "Get information about the authenticated user", "operationId": "getUser", "responses": {"200": {"description": "User information", "content": {"application/json": {"schema": {"type": "object", "properties": {"login": {"type": "string"}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "avatar_url": {"type": "string", "nullable": true}}}}}}, "401": {"description": "Authentication error", "content": {"application/json": {"schema": {"type": "string"}}}}, "500": {"description": "Unknown error", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/user/search/repositories": {"get": {"summary": "Search repositories", "description": "Search for repositories", "operationId": "searchRepositories", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Search query"}, {"name": "per_page", "in": "query", "required": false, "schema": {"type": "integer", "default": 5}, "description": "Number of repositories to return per page"}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "string", "default": "stars"}, "description": "Sort order for repositories"}, {"name": "order", "in": "query", "required": false, "schema": {"type": "string", "default": "desc"}, "description": "Sort direction"}], "responses": {"200": {"description": "Search results", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"full_name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "html_url": {"type": "string"}, "private": {"type": "boolean"}, "fork": {"type": "boolean"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}}, "401": {"description": "Authentication error", "content": {"application/json": {"schema": {"type": "string"}}}}, "500": {"description": "Unknown error", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/user/suggested-tasks": {"get": {"summary": "Get suggested tasks", "description": "Get suggested tasks for the authenticated user across their most recently pushed repositories", "operationId": "getSuggestedTasks", "responses": {"200": {"description": "Suggested tasks", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "url": {"type": "string"}, "repository": {"type": "string"}, "type": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}, "401": {"description": "Authentication error", "content": {"application/json": {"schema": {"type": "string"}}}}, "500": {"description": "Unknown error", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/settings": {"get": {"summary": "Get settings", "description": "Get user settings", "operationId": "loadSettings", "responses": {"200": {"description": "User settings", "content": {"application/json": {"schema": {"type": "object", "properties": {"language": {"type": "string"}, "agent": {"type": "string"}, "security_analyzer": {"type": "string"}, "confirmation_mode": {"type": "boolean"}, "llm_model": {"type": "string"}, "llm_api_key_set": {"type": "boolean"}, "llm_base_url": {"type": "string", "nullable": true}, "remote_runtime_resource_factor": {"type": "number"}, "enable_default_condenser": {"type": "boolean"}, "enable_sound_notifications": {"type": "boolean"}, "user_consents_to_analytics": {"type": "boolean"}, "provider_tokens_set": {"type": "object", "additionalProperties": {"type": "boolean"}}}}}}}, "401": {"description": "Invalid token", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "404": {"description": "Settings not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}, "post": {"summary": "Store settings", "description": "Store user settings", "operationId": "storeSettings", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"language": {"type": "string"}, "agent": {"type": "string"}, "security_analyzer": {"type": "string"}, "confirmation_mode": {"type": "boolean"}, "llm_model": {"type": "string"}, "llm_api_key": {"type": "string"}, "llm_base_url": {"type": "string", "nullable": true}, "remote_runtime_resource_factor": {"type": "number"}, "enable_default_condenser": {"type": "boolean"}, "enable_sound_notifications": {"type": "boolean"}, "user_consents_to_analytics": {"type": "boolean"}, "provider_tokens": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}, "responses": {"200": {"description": "Settings stored successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "401": {"description": "Invalid token", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Error storing settings", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/reset-settings": {"post": {"summary": "Reset settings (Deprecated)", "description": "This endpoint is deprecated and will return a 410 Gone error. Reset functionality has been removed.", "operationId": "resetSettings", "deprecated": true, "responses": {"410": {"description": "Feature removed", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Reset settings functionality has been removed."}}}}}}}}}, "/api/unset-settings-tokens": {"post": {"summary": "Unset settings tokens", "description": "Unset provider tokens in settings", "operationId": "unsetSettingsTokens", "responses": {"200": {"description": "Tokens unset successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "500": {"description": "Error unsetting tokens", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/api/options/models": {"get": {"summary": "Get models", "description": "Get all models supported by LiteLLM", "operationId": "getLitellmModels", "responses": {"200": {"description": "List of models", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/options/agents": {"get": {"summary": "Get agents", "description": "Get all agents supported by OpenHands", "operationId": "getAgents", "responses": {"200": {"description": "List of agents", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/options/security-analyzers": {"get": {"summary": "Get security analyzers", "description": "Get all supported security analyzers", "operationId": "getSecurityAnalyzers", "responses": {"200": {"description": "List of security analyzers", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/options/config": {"get": {"summary": "Get config", "description": "Get current server configuration", "operationId": "getConfig", "responses": {"200": {"description": "Server configuration", "content": {"application/json": {"schema": {"type": "object"}}}}}}}}, "components": {"schemas": {"Repository": {"type": "object", "properties": {"full_name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "html_url": {"type": "string"}, "private": {"type": "boolean"}, "fork": {"type": "boolean"}, "updated_at": {"type": "string", "format": "date-time"}, "owner_type": {"type": "string", "enum": ["user", "organization"], "nullable": true}}}, "User": {"type": "object", "properties": {"login": {"type": "string"}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "avatar_url": {"type": "string", "nullable": true}}}, "SuggestedTask": {"type": "object", "properties": {"title": {"type": "string"}, "url": {"type": "string"}, "repository": {"type": "string"}, "type": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}, "ConversationInfo": {"type": "object", "properties": {"conversation_id": {"type": "string"}, "title": {"type": "string"}, "last_updated_at": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "selected_repository": {"type": "string", "nullable": true}, "status": {"type": "string", "enum": ["RUNNING", "STOPPED"]}, "trigger": {"type": "string", "enum": ["GUI", "API"]}}}, "ConversationInfoResultSet": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/components/schemas/ConversationInfo"}}, "next_page_id": {"type": "string", "nullable": true}}}, "FeedbackDataModel": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "version": {"type": "string"}, "permissions": {"type": "string", "default": "private"}, "polarity": {"type": "string"}, "feedback": {"type": "string"}, "trajectory": {"type": "array", "items": {"type": "object"}}}}, "Settings": {"type": "object", "properties": {"language": {"type": "string"}, "agent": {"type": "string"}, "security_analyzer": {"type": "string"}, "confirmation_mode": {"type": "boolean"}, "llm_model": {"type": "string"}, "llm_api_key": {"type": "string"}, "llm_base_url": {"type": "string", "nullable": true}, "remote_runtime_resource_factor": {"type": "number"}, "enable_default_condenser": {"type": "boolean"}, "enable_sound_notifications": {"type": "boolean"}, "user_consents_to_analytics": {"type": "boolean"}}}, "GETSettingsModel": {"type": "object", "properties": {"language": {"type": "string"}, "agent": {"type": "string"}, "security_analyzer": {"type": "string"}, "confirmation_mode": {"type": "boolean"}, "llm_model": {"type": "string"}, "llm_api_key_set": {"type": "boolean"}, "llm_base_url": {"type": "string", "nullable": true}, "remote_runtime_resource_factor": {"type": "number"}, "enable_default_condenser": {"type": "boolean"}, "enable_sound_notifications": {"type": "boolean"}, "user_consents_to_analytics": {"type": "boolean"}, "provider_tokens_set": {"type": "object", "additionalProperties": {"type": "boolean"}}}}, "POSTSettingsModel": {"type": "object", "properties": {"language": {"type": "string"}, "agent": {"type": "string"}, "security_analyzer": {"type": "string"}, "confirmation_mode": {"type": "boolean"}, "llm_model": {"type": "string"}, "llm_api_key": {"type": "string"}, "llm_base_url": {"type": "string", "nullable": true}, "remote_runtime_resource_factor": {"type": "number"}, "enable_default_condenser": {"type": "boolean"}, "enable_sound_notifications": {"type": "boolean"}, "user_consents_to_analytics": {"type": "boolean"}, "provider_tokens": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"bearerAuth": []}]}