---
title: Organization and User Microagents
description: Organizations and users can define microagents that apply to all repositories belonging to the organization or user.
---

## Usage

These microagents can be [any type of microagent](./microagents-overview#microagent-types) and will be loaded
accordingly. However, they are applied to all repositories belonging to the organization or user.

Add a `.openhands` repository under the organization or user and create a `microagents` directory and place the
microagents in that directory.

For GitLab organizations, use `openhands-config` as the repository name instead of `.openhands`, since GitLab doesn't support repository names starting with non-alphanumeric characters.

## Example

General microagent file example for organization `Great-Co` located inside the `.openhands` repository:
`microagents/org-microagent.md`:
```
* Use type hints and error boundaries; validate inputs at system boundaries and fail with meaningful error messages.
* Document interfaces and public APIs; use implementation comments only for non-obvious logic.
* Follow the same naming convention for variables, classes, constants, etc. already used in each repository.
```

For GitLab organizations, the same microagent would be located inside the `openhands-config` repository.

## User Microagents When Running Openhands on Your Own

<Note>
  This works with CLI, headless and development modes. It does not work out of the box when running OpenHands using the docker command.
</Note>

When running OpenHands on your own, you can place microagents in the `~/.openhands/microagents` folder on your local
system and OpenHands will always load it for all your conversations.
