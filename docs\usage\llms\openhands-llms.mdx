---
title: OpenHands
description: OpenHands LLM provider with access to state-of-the-art (SOTA) agentic coding models.
---

## Obtain Your OpenHands LLM API Key

1. [Log in to OpenHands Cloud](/usage/cloud/openhands-cloud).
2. Go to the Settings page and navigate to the `API Keys` tab.
3. Copy your `LLM API Key`.

![OpenHands LLM API Key](/static/img/openhands-llm-api-key.png)

## Configuration

When running OpenHands, you'll need to set the following in the OpenHands UI through the Settings under the `LLM` tab:
- `LLM Provider` to `OpenHands`
- `LLM Model` to the model you will be using (e.g. claude-sonnet-4-20250514)
- `API Key` to your OpenHands LLM API key copied from above

## Using OpenHands LLM Provider in the CLI

1. [Run OpenHands CLI](/usage/how-to/cli-mode).
2. To select OpenHands as the LLM provider:
  - If this is your first time running the CLI, choose `openhands` and then select the model that you would like to use.
  - If you have previously run the CLI, run the `/settings` command and select to modify the `Basic` settings. Then
    choose `openhands` and finally the model.

![OpenHands Provider in CLI](/static/img/openhands-provider-cli.png)

## Pricing

Pricing follows official API provider rates.
[You can view model prices here.](https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json)
