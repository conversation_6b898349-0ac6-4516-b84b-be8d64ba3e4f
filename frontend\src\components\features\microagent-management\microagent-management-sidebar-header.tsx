import { useTranslation } from "react-i18next";
import { <PERSON>18n<PERSON><PERSON> } from "#/i18n/declaration";
import QuestionCircleIcon from "#/icons/question-circle.svg?react";
import { DOCUMENTATION_URL } from "#/utils/constants";

export function MicroagentManagementSidebarHeader() {
  const { t } = useTranslation();

  return (
    <div>
      <h1 className="text-white text-[28px] font-bold">
        {t(I18nKey.MICROAGENT_MANAGEMENT$DESCRIPTION)}
      </h1>
      <p className="text-white text-sm font-normal leading-[20px] pt-2">
        {t(I18nKey.MICROAGENT_MANAGEMENT$USE_MICROAGENTS)}
        <a
          href={DOCUMENTATION_URL.MICROAGENTS.MICROAGENTS_OVERVIEW}
          target="_blank"
          rel="noopener noreferrer"
        >
          <QuestionCircleIcon className="inline-block ml-1" />
        </a>
      </p>
    </div>
  );
}
