---
title: FAQs
description: Frequently asked questions about OpenHands
icon: question
---

## Getting Started

### I'm new to OpenHands. Where should I start?

1. **Quick start**: Use [OpenHands Cloud](/usage/cloud/openhands-cloud) to get started quickly with
  [GitHub](/usage/cloud/github-installation), [GitLab](/usage/cloud/gitlab-installation),
  and [Slack](/usage/cloud/slack-installation) integrations.
2. **Run on your own**: If you prefer to run it on your own hardware, follow our [Getting Started guide](/usage/local-setup).
3. **First steps**: Read over the [start building guidelines](/usage/getting-started) and
  [prompting best practices](/usage/prompting/prompting-best-practices) to learn the basics.

### Can I use OpenHands for production workloads?

OpenHands is meant to be run by a single user on their local workstation. It is not appropriate for multi-tenant
deployments where multiple users share the same instance. There is no built-in authentication, isolation, or scalability.

If you're interested in running OpenHands in a multi-tenant environment, check out the source-available,
commercially-licensed [OpenHands Cloud Helm Chart](https://github.com/all-Hands-AI/OpenHands-cloud).

<Info>
Using OpenHands for work? We'd love to chat! Fill out
[this short form](https://docs.google.com/forms/d/e/1FAIpQLSet3VbGaz8z32gW9Wm-Grl4jpt5WgMXPgJ4EDPVmCETCBpJtQ/viewform)
to join our Design Partner program, where you'll get early access to commercial features and the opportunity to provide
input on our product roadmap.
</Info>

## Safety and Security

### It's doing stuff without asking, is that safe?

**Generally yes, but with important considerations.** OpenHands runs all code in a secure, isolated Docker container
(called a "sandbox") that is separate from your host system. However, the safety depends on your configuration:

**What's protected:**
- Your host system files and programs (unless you mount them using [this feature](/usage/runtimes/docker#connecting-to-your-filesystem))
- Host system resources
- Other containers and processes

**Potential risks to consider:**
- The agent can access the internet from within the container.
- If you provide credentials (API keys, tokens), the agent can use them.
- Mounted files and directories can be modified or deleted.
- Network requests can be made to external services.

For detailed security information, see our [Runtime Architecture](/usage/architecture/runtime),
[Security Configuration](/usage/configuration-options#security-configuration),
and [Hardened Docker Installation](/usage/runtimes/docker#hardened-docker-installation) documentation.

## File Storage and Access

### Where are my files stored?

Your files are stored in different locations depending on how you've configured OpenHands:

**Default behavior (no file mounting):**
- Files created by the agent are stored inside the runtime Docker container.
- These files are temporary and will be lost when the container is removed.
- The agent works in the `/workspace` directory inside the runtime container.

**When you mount your local filesystem (following [this](/usage/runtimes/docker#connecting-to-your-filesystem)):**
- Your local files are mounted into the container's `/workspace` directory.
- Changes made by the agent are reflected in your local filesystem.
- Files persist after the container is stopped.

<Warning>
Be careful when mounting your filesystem - the agent can modify or delete any files in the mounted directory.
</Warning>

## Development Tools and Environment

### How do I get the dev tools I need?

OpenHands comes with a basic runtime environment that includes Python and Node.js.
It also has the ability to install any tools it needs, so usually it's sufficient to ask it to set up its environment.

If you would like to set things up more systematically, you can:
- **Use setup.sh**: Add a [setup.sh file](/usage/prompting/repository#setup-script) file to
  your repository, which will be run every time the agent starts.
- **Use a custom sandbox**: Use a [custom docker image](/usage/how-to/custom-sandbox-guide) to initialize the sandbox.

### Something's not working. Where can I get help?

1. **Search existing issues**: Check our [GitHub issues](https://github.com/All-Hands-AI/OpenHands/issues) to see if
  others have encountered the same problem.
2. **Join our community**: Get help from other users and developers:
   - [Slack community](https://join.slack.com/t/openhands-ai/shared_invite/zt-3847of6xi-xuYJIPa6YIPg4ElbDWbtSA)
   - [Discord server](https://discord.gg/ESHStjSjD4)
3. **Check our troubleshooting guide**: Common issues and solutions are documented in
  [Troubleshooting](/usage/troubleshooting/troubleshooting).
4. **Report bugs**: If you've found a bug, please [create an issue](https://github.com/All-Hands-AI/OpenHands/issues/new)
  and fill in as much detail as possible.
