{"$schema": "https://mintlify.com/docs.json", "theme": "mint", "name": "All Hands Docs", "colors": {"primary": "#99873c", "light": "#ffe165", "dark": "#ffe165"}, "background": {"color": {"light": "#f7f3ee", "dark": "#0B0D0E"}}, "appearance": {"default": "light"}, "favicon": "/logo-square.png", "navigation": {"tabs": [{"tab": "Docs", "pages": ["index", "usage/installation", "usage/getting-started", "usage/key-features", "usage/faqs", {"group": "OpenHands Cloud", "pages": ["usage/cloud/openhands-cloud", {"group": "Integrations", "pages": ["usage/cloud/bitbucket-installation", "usage/cloud/github-installation", "usage/cloud/gitlab-installation", "usage/cloud/slack-installation"]}, "usage/cloud/cloud-ui", "usage/cloud/cloud-api"]}, {"group": "Run OpenHands on Your Own", "pages": ["usage/local-setup", "usage/how-to/gui-mode", "usage/how-to/cli-mode", "usage/how-to/headless-mode", "usage/how-to/github-action", {"group": "Advanced Configuration", "pages": [{"group": "LLM Configuration", "pages": ["usage/llms/llms", {"group": "Providers", "pages": ["usage/llms/azure-llms", "usage/llms/google-llms", "usage/llms/groq", "usage/llms/local-llms", "usage/llms/litellm-proxy", "usage/llms/moonshot", "usage/llms/openai-llms", "usage/llms/openhands-llms", "usage/llms/openrouter"]}]}, {"group": "Runtime Configuration", "pages": ["usage/runtimes/overview", {"group": "Providers", "pages": ["usage/runtimes/docker", "usage/runtimes/remote", "usage/runtimes/local", {"group": "Third-Party Providers", "pages": ["usage/runtimes/modal", "usage/runtimes/daytona", "usage/runtimes/runloop", "usage/runtimes/e2b"]}]}]}, "usage/configuration-options", "usage/how-to/custom-sandbox-guide", "usage/search-engine-setup", "usage/mcp"]}]}, {"group": "Customizations & Settings", "pages": ["usage/common-settings", "usage/prompting/repository", {"group": "Microagents", "pages": ["usage/prompting/microagents-overview", "usage/prompting/microagents-repo", "usage/prompting/microagents-keyword", "usage/prompting/microagents-org", "usage/prompting/microagents-public"]}]}, {"group": "Tips and Tricks", "pages": ["usage/prompting/prompting-best-practices"]}, {"group": "Troubleshooting & Feedback", "pages": ["usage/troubleshooting/troubleshooting", "usage/feedback"]}, {"group": "OpenHands Developers", "pages": ["usage/how-to/development-overview", {"group": "Architecture", "pages": ["usage/architecture/backend", "usage/architecture/runtime"]}, "usage/how-to/debugging", "usage/how-to/evaluation-harness", "usage/how-to/websocket-connection"]}]}, {"tab": "Success Stories", "pages": ["success-stories/index"]}, {"tab": "API Reference", "openapi": "/openapi.json"}], "global": {"anchors": [{"anchor": "Company", "href": "https://www.all-hands.dev/", "icon": "house"}, {"anchor": "Blog", "href": "https://www.all-hands.dev/blog", "icon": "newspaper"}, {"anchor": "OpenHands Cloud", "href": "https://app.all-hands.dev", "icon": "cloud"}]}}, "logo": {"light": "/logo/light.svg", "dark": "/logo/dark.svg"}, "navbar": {"links": [], "primary": {"type": "github", "href": "https://github.com/All-Hands-AI/OpenHands"}}, "footer": {"socials": {"slack": "https://join.slack.com/t/openhands-ai/shared_invite/zt-3847of6xi-xuYJIPa6YIPg4ElbDWbtSA", "github": "https://github.com/All-Hands-AI/OpenHands", "discord": "https://discord.gg/ESHStjSjD4"}}, "contextual": {"options": ["copy", "view", "chatgpt", "claude"]}, "redirects": [{"source": "/modules/:slug*", "destination": "/:slug*"}]}