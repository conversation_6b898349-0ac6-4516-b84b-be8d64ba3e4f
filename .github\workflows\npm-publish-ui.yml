name: Publish OpenHands UI Package

# * Always run on "main"
# * Run on PRs that have changes in the "openhands-ui" folder or this workflow
on:
  push:
    branches:
      - main
    paths:
      - "openhands-ui/**"
      - ".github/workflows/npm-publish-ui.yml"

# If triggered by a PR, it will be in the same group. However, each commit on main will be in its own unique group
concurrency:
  group: npm-publish-ui
  cancel-in-progress: false

jobs:
  check-version:
    name: Check if version has changed
    runs-on: blacksmith-4vcpu-ubuntu-2204
    defaults:
      run:
        shell: bash
    outputs:
      should-publish: ${{ steps.version-check.outputs.should-publish }}
      current-version: ${{ steps.version-check.outputs.current-version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # Need previous commit to compare

      - name: Check if version changed
        id: version-check
        run: |
          # Get current version from package.json
          CURRENT_VERSION=$(jq -r .version openhands-ui/package.json)
          echo "current-version=$CURRENT_VERSION" >> $GITHUB_OUTPUT

          # Check if package.json version changed in this commit
          if git diff HEAD~1 HEAD --name-only | grep -q "openhands-ui/package.json"; then
            # Check if the version field specifically changed
            if git diff HEAD~1 HEAD openhands-ui/package.json | grep -q '"version"'; then
              echo "Version changed in package.json, will publish"
              echo "should-publish=true" >> $GITHUB_OUTPUT
            else
              echo "package.json changed but version did not change, skipping publish"
              echo "should-publish=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "package.json did not change, skipping publish"
            echo "should-publish=false" >> $GITHUB_OUTPUT
          fi

  publish:
    name: Publish to npm
    runs-on: blacksmith-4vcpu-ubuntu-2204
    needs: check-version
    if: needs.check-version.outputs.should-publish == 'true'
    defaults:
      run:
        shell: bash
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version-file: "openhands-ui/.bun-version"

      - name: Install dependencies
        working-directory: ./openhands-ui
        run: bun install --frozen-lockfile

      - name: Build package
        working-directory: ./openhands-ui
        run: bun run build

      - name: Check if package already exists on npm
        id: npm-check
        working-directory: ./openhands-ui
        run: |
          PACKAGE_NAME=$(jq -r .name package.json)
          VERSION="${{ needs.check-version.outputs.current-version }}"

          # Check if this version already exists on npm
          if npm view "$PACKAGE_NAME@$VERSION" version 2>/dev/null; then
            echo "Version $VERSION already exists on npm, skipping publish"
            echo "already-exists=true" >> $GITHUB_OUTPUT
          else
            echo "Version $VERSION does not exist on npm, proceeding with publish"
            echo "already-exists=false" >> $GITHUB_OUTPUT
          fi

      - name: Setup npm authentication
        if: steps.npm-check.outputs.already-exists == 'false'
        run: |
          echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Publish to npm
        if: steps.npm-check.outputs.already-exists == 'false'
        working-directory: ./openhands-ui
        run: |
          # The prepublishOnly script will run automatically and build the package
          npm publish
          echo "✅ Successfully published @openhands/ui@${{ needs.check-version.outputs.current-version }} to npm"
