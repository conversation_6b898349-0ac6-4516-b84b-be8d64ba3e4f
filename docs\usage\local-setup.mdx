---
title: Getting Started
description: Getting started with running OpenHands on your own.
---

## Recommended Methods for Running Openhands on Your Local System

### System Requirements

- MacOS with [Docker Desktop support](https://docs.docker.com/desktop/setup/install/mac-install/#system-requirements)
- Linux
- Windows with [WSL](https://learn.microsoft.com/en-us/windows/wsl/install) and [Docker Desktop support](https://docs.docker.com/desktop/setup/install/windows-install/#system-requirements)
- Windows without WSL (see [Windows Without WSL Guide](/usage/windows-without-wsl))

A system with a modern processor and a minimum of **4GB RAM** is recommended to run OpenHands.

### Prerequisites

<AccordionGroup>

<Accordion title="MacOS">

  **Docker Desktop**

  1. [Install Docker Desktop on Mac](https://docs.docker.com/desktop/setup/install/mac-install).
  2. Open Docker Desktop, go to `Settings > Advanced` and ensure `Allow the default Docker socket to be used` is enabled.
</Accordion>

<Accordion title="Linux">

  <Note>
  Tested with Ubuntu 22.04.
  </Note>

  **Docker Desktop**

  1. [Install Docker Desktop on Linux](https://docs.docker.com/desktop/setup/install/linux/).

</Accordion>

<Accordion title="Windows">

  **WSL**

  1. [Install WSL](https://learn.microsoft.com/en-us/windows/wsl/install).
  2. Run `wsl --version` in powershell and confirm `Default Version: 2`.

  **Docker Desktop**

  1. [Install Docker Desktop on Windows](https://docs.docker.com/desktop/setup/install/windows-install).
  2. Open Docker Desktop, go to `Settings` and confirm the following:
  - General: `Use the WSL 2 based engine` is enabled.
  - Resources > WSL Integration: `Enable integration with my default WSL distro` is enabled.

  <Note>
  The docker command below to start the app must be run inside the WSL terminal.
  </Note>

  **Alternative: Windows without WSL**

  If you prefer to run OpenHands on Windows without WSL or Docker, see our [Windows Without WSL Guide](/usage/windows-without-wsl).

</Accordion>

</AccordionGroup>

### Start the App

```bash
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.50-nikolaik

docker run -it --rm --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.50-nikolaik \
    -e LOG_ALL_EVENTS=true \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v ~/.openhands:/.openhands \
    -p 3000:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app \
    docker.all-hands.dev/all-hands-ai/openhands:0.50
```

> **Note**: If you used OpenHands before version 0.44, you may want to run `mv ~/.openhands-state ~/.openhands` to migrate your conversation history to the new location.

You'll find OpenHands running at http://localhost:3000!

### Setup

After launching OpenHands, you **must** select an `LLM Provider` and `LLM Model` and enter a corresponding `API Key`.
This can be done during the initial settings popup or by selecting the `Settings`
button (gear icon) in the UI.

If the required model does not exist in the list, in `Settings` under the `LLM` tab, you can toggle `Advanced` options
and manually enter it with the correct prefix in the `Custom Model` text box.
The `Advanced` options also allow you to specify a `Base URL` if required.

#### Getting an API Key

OpenHands requires an API key to access most language models. Here's how to get an API key from the recommended providers:

<AccordionGroup>

<Accordion title="Anthropic (Claude)">

1. [Create an Anthropic account](https://console.anthropic.com/).
2. [Generate an API key](https://console.anthropic.com/settings/keys).
3. [Set up billing](https://console.anthropic.com/settings/billing).

</Accordion>

<Accordion title="OpenAI">

1. [Create an OpenAI account](https://platform.openai.com/).
2. [Generate an API key](https://platform.openai.com/api-keys).
3. [Set up billing](https://platform.openai.com/account/billing/overview).

</Accordion>

<Accordion title="Google (Gemini)">

1. Create a Google account if you don't already have one.
2. [Generate an API key](https://aistudio.google.com/apikey).
3. [Set up billing](https://aistudio.google.com/usage?tab=billing).

</Accordion>

<Accordion title="Local LLM (e.g. LM Studio, llama.cpp, Ollama)">

If your local LLM server isn’t behind an authentication proxy, you can enter any value as the API key (e.g. `local-key`, `test123`) — it won’t be used.

</Accordion>

</AccordionGroup>

Consider setting usage limits to control costs.

#### Using a Local LLM

<Note>
Effective use of local models for agent tasks requires capable hardware, along with models specifically tuned for instruction-following and agent-style behavior.
</Note>

To run OpenHands with a locally hosted language model instead of a cloud provider, see the [Local LLMs guide](/usage/llms/local-llms) for setup instructions.

#### Setting Up Search Engine

OpenHands can be configured to use a search engine to allow the agent to search the web for information when needed.

To enable search functionality in OpenHands:

1. Get a Tavily API key from [tavily.com](https://tavily.com/).
2. Enter the Tavily API key in the Settings page under `LLM` tab > `Search API Key (Tavily)`

For more details, see the [Search Engine Setup](/usage/search-engine-setup) guide.

### Versions

The [docker command above](/usage/local-setup#start-the-app) pulls the most recent stable release of OpenHands. You have other options as well:
- For a specific release, replace `$VERSION` in `openhands:$VERSION` and `runtime:$VERSION`, with the version number.
For example, `0.9` will automatically point to the latest `0.9.x` release, and `0` will point to the latest `0.x.x` release.
- For the most up-to-date development version, replace `$VERSION` in `openhands:$VERSION` and `runtime:$VERSION`, with `main`.
This version is unstable and is recommended for testing or development purposes only.

## Next Steps

- [Connect OpenHands to your local filesystem.](/usage/runtimes/docker#connecting-to-your-filesystem) to use OpenHands with your GitHub repositories
- [Run OpenHands in a scriptable headless mode.](/usage/how-to/headless-mode)
- [Run OpenHands with a friendly CLI.](/usage/how-to/cli-mode)
- [Run OpenHands on tagged issues with a GitHub action.](/usage/how-to/github-action)
