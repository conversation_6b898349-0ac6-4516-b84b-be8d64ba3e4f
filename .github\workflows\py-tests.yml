# Workflow that runs python tests
name: Run Python Tests

# The jobs in this workflow are required, so they must run at all times
# * Always run on "main"
# * Always run on PRs
on:
  push:
    branches:
      - main
  pull_request:

# If triggered by a PR, it will be in the same group. However, each commit on main will be in its own unique group
concurrency:
  group: ${{ github.workflow }}-${{ (github.head_ref && github.ref) || github.run_id }}
  cancel-in-progress: true

jobs:
  # Run python tests on Linux
  test-on-linux:
    name: Python Tests on Linux
    runs-on: blacksmith-4vcpu-ubuntu-2204
    env:
      INSTALL_DOCKER: '0' # Set to '0' to skip Docker installation
    strategy:
      matrix:
        python-version: ['3.12']
    steps:
      - uses: actions/checkout@v4
      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3
      - name: Install tmux
        run: sudo apt-get update && sudo apt-get install -y tmux
      - name: Setup Node.js
        uses: useblacksmith/setup-node@v5
        with:
          node-version: '22.x'
      - name: Install poetry via pipx
        run: pipx install poetry
      - name: Set up Python
        uses: useblacksmith/setup-python@v6
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'poetry'
      - name: Install Python dependencies using Poetry
        run: poetry install --with dev,test,runtime
      - name: Build Environment
        run: make build
      - name: Run Unit Tests
        run: poetry run pytest --forked -n auto -svv ./tests/unit
      - name: Run Runtime Tests with CLIRuntime
        run: TEST_RUNTIME=cli poetry run pytest -svv tests/runtime/test_bash.py
      - name: Run E2E Tests
        run: poetry run pytest -svv tests/e2e

  # Run specific Windows python tests
  test-on-windows:
    name: Python Tests on Windows
    runs-on: windows-latest
    strategy:
      matrix:
        python-version: ['3.12']
    steps:
      - uses: actions/checkout@v4
      - name: Install pipx
        run: pip install pipx
      - name: Install poetry via pipx
        run: pipx install poetry
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'poetry'
      - name: Install Python dependencies using Poetry
        run: poetry install --with dev,test,runtime
      - name: Run Windows unit tests
        run: poetry run pytest -svv tests/unit/test_windows_bash.py
        env:
          DEBUG: "1"
      - name: Run Windows runtime tests with LocalRuntime
        run: $env:TEST_RUNTIME="local"; poetry run pytest -svv tests/runtime/test_bash.py
        env:
          TEST_RUNTIME: local
          DEBUG: "1"
