repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        exclude: ^(docs/|modules/|python/|openhands-ui/|third_party/)
      - id: end-of-file-fixer
        exclude: ^(docs/|modules/|python/|openhands-ui/|third_party/)
      - id: check-yaml
        args: ["--allow-multiple-documents"]
      - id: debug-statements

  - repo: https://github.com/tox-dev/pyproject-fmt
    rev: v2.5.1
    hooks:
      - id: pyproject-fmt
  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.24.1
    hooks:
      - id: validate-pyproject

  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.11.8
    hooks:
      # Run the linter.
      - id: ruff
        entry: ruff check --config dev_config/python/ruff.toml
        types_or: [python, pyi, jupyter]
        args: [--fix, --unsafe-fixes]
        exclude: third_party/
      # Run the formatter.
      - id: ruff-format
        entry: ruff format --config dev_config/python/ruff.toml
        types_or: [python, pyi, jupyter]
        exclude: third_party/

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.15.0
    hooks:
      - id: mypy
        additional_dependencies:
          [types-requests, types-setuptools, types-pyyaml, types-toml, types-docker, pydantic, lxml]
        # To see gaps add `--html-report mypy-report/`
        entry: mypy --config-file dev_config/python/mypy.ini openhands/
        always_run: true
        pass_filenames: false
