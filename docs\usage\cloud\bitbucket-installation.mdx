---
title: Bitbucket Integration
description: This guide walks you through the process of installing OpenHands Cloud for your Bitbucket repositories. Once
  set up, it will allow OpenHands to work with your Bitbucket repository.
---

## Prerequisites

- Signed in to [OpenHands Cloud](https://app.all-hands.dev) with [a Bitbucket account](/usage/cloud/openhands-cloud).

## Adding Bitbucket Repository Access

Upon signing into OpenHands Cloud with a Bitbucket account, OpenHands will have access to your repositories.

## Working With Bitbucket Repos in Openhands Cloud

After signing in with a Bitbucket account, use the `select a repo` and `select a branch` dropdowns to select the
appropriate repository and branch you'd like OpenHands to work on. Then click on `Launch` to start the conversation!

![Connect Repo](/static/img/connect-repo-no-github.png)

## Next Steps

- [Learn about the Cloud UI](/usage/cloud/cloud-ui).
- [Use the Cloud API](/usage/cloud/cloud-api) to programmatically interact with OpenHands.
