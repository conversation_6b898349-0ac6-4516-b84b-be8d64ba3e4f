---
title: Runloop Runtime
description: Runloop provides a fast, secure and scalable AI sandbox (Devbox). Check out the
  [runloop docs](https://docs.runloop.ai/overview/what-is-runloop) for more detail.
---

## Access
Runloop is currently available in a closed beta. For early access, or
just to say hello, sign up at https://www.runloop.ai/hello

## Set up
With your runloop API,
```bash
export RUNLOOP_API_KEY=<your-api-key>
```

Configure the runtime
```bash
export RUNTIME="runloop"
```

## Interact with your devbox
Runloop provides additional tools to interact with your Devbox based
runtime environment. See the [docs](https://docs.runloop.ai/tools) for an up
to date list of tools.

### Dashboard
View logs, ssh into, or view your Devbox status from the [dashboard](https://platform.runloop.ai)

### CLI
Use the Runloop CLI to view logs, execute commands, and more.
See the setup instructions [here](https://docs.runloop.ai/tools/cli)
