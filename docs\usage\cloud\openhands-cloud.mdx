---
title: Getting Started
description: Getting started with OpenHands Cloud.
---

## Accessing OpenHands Cloud

OpenHands Cloud is the hosted cloud version of All Hands AI's OpenHands. To get started with OpenHands Cloud,
visit [app.all-hands.dev](https://app.all-hands.dev).

You'll be prompted to connect with your GitHub, GitLab or Bitbucket account:

1. Click `Log in with GitHub`, `Log in with G<PERSON><PERSON>ab` or `Log in with Bitbucket`.
2. Review the permissions requested by OpenHands and authorize the application.
   - OpenHands will require certain permissions from your account. To read more about these permissions,
     you can click the `Learn more` link on the authorization page.
3. Review and accept the `terms of service` and select `Continue`.

## Next Steps

Once you've connected your account, you can:

- [Install GitHub Integration](/usage/cloud/github-installation) to use OpenHands with your GitHub repositories.
- [Install GitLab Integration](/usage/cloud/gitlab-installation) to use OpenHands with your GitLab repositories.
- [Install Bitbucket Integration](/usage/cloud/bitbucket-installation) to use OpenHands with your Bitbucket repositories.
- [Learn about the Cloud UI](/usage/cloud/cloud-ui).
- [Use the Cloud API](/usage/cloud/cloud-api) to programmatically interact with OpenHands.
